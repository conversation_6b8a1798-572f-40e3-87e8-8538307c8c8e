
// 简化的内容脚本 - 专注于页面内容提取

// 将HTML转换为保持基本格式的文本（适配Flomo）
function htmlToFormattedText(element) {
  // 创建一个临时容器来处理HTML
  const tempDiv = document.createElement('div');
  tempDiv.appendChild(element.cloneNode(true));

  // 处理各种HTML元素，只保留段落结构、粗体和下划线
  return processElement(tempDiv);
}

// 将函数暴露到全局，供background.js调用
window.htmlToFormattedText = htmlToFormattedText;

// 保持代码块的原始格式和缩进
function preserveCodeFormatting(codeElement) {
  // 获取代码的原始文本内容，保持空白字符
  let codeText = '';

  function extractText(node) {
    if (node.nodeType === Node.TEXT_NODE) {
      return node.textContent;
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      let text = '';
      for (const child of node.childNodes) {
        if (child.tagName?.toLowerCase() === 'br') {
          text += '\n';
        } else {
          text += extractText(child);
        }
      }
      return text;
    }
    return '';
  }

  codeText = extractText(codeElement);

  // 保持原始的空白字符，但去除首尾的空行
  return codeText.replace(/^\n+|\n+$/g, '');
}

// 递归处理元素，只保留基本格式（段落、粗体、下划线）
function processElement(element) {
  let result = '';

  for (const node of element.childNodes) {
    if (node.nodeType === Node.TEXT_NODE) {
      // 文本节点：保持原始文本，但清理多余空白
      const text = node.textContent;
      result += text;
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const tagName = node.tagName.toLowerCase();
      const content = processElement(node);

      switch (tagName) {
        // 标题处理 - 转换为普通文本，保持内容
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
          result += `\n${content.trim()}\n\n`;
          break;

        // 段落处理
        case 'p':
          result += `${content.trim()}\n\n`;
          break;

        // 换行处理
        case 'br':
          result += '\n';
          break;

        // 列表处理 - 转换为普通文本，保持内容
        case 'ul':
        case 'ol':
          result += `\n${content}`;
          // 如果列表后面还有内容，添加换行
          if (node.nextSibling && node.nextSibling.nodeType !== Node.TEXT_NODE) {
            result += '\n';
          }
          break;
        case 'li':
          // 列表项转换为普通文本，保持缩进
          const indent = getListIndent(node);
          const trimmedContent = content.trim();
          result += `${indent}${trimmedContent}\n`;
          break;

        // 代码处理 - 转换为普通文本，保持内容
        case 'code':
        case 'pre':
          // 代码转换为普通文本，保持原始格式
          const codeContent = preserveCodeFormatting(node);
          result += `\n${codeContent}\n\n`;
          break;

        // 引用处理 - 转换为普通文本，保持内容
        case 'blockquote':
          result += `\n${content.trim()}\n\n`;
          break;

        // 文本样式处理 - 只保留粗体和下划线的HTML格式
        case 'strong':
        case 'b':
          result += `<strong>${content}</strong>`;
          break;
        case 'u':
          result += `<u>${content}</u>`;
          break;

        // 其他样式转换为普通文本
        case 'em':
        case 'i':
        case 'del':
        case 's':
        case 'strike':
        case 'mark':
        case 'sup':
        case 'sub':
        case 'kbd':
          result += content;
          break;

        // 链接处理 - 转换为普通文本
        case 'a':
          result += content;
          break;

        // 分隔线 - 转换为普通文本
        case 'hr':
          result += '\n\n';
          break;

        // 表格处理 - 转换为普通文本
        case 'table':
          result += `\n${content}\n`;
          break;
        case 'tr':
          result += `${content}\n`;
          break;
        case 'td':
        case 'th':
          result += `${content.trim()} `;
          break;

        // 其他块级元素
        case 'div':
        case 'section':
        case 'article':
        case 'header':
        case 'footer':
        case 'main':
          result += content;
          // 如果div后面还有内容，添加换行
          if (content.trim() && node.nextSibling) {
            result += '\n';
          }
          break;

        // 默认处理
        default:
          result += content;
      }
    }
  }

  return result;
}

// 获取列表项的层级
function getListLevel(listItem) {
  let level = 0;
  let current = listItem.parentElement;

  while (current && level < 10) { // 防止无限循环
    if (current.tagName && ['UL', 'OL'].includes(current.tagName.toLowerCase())) {
      level++;
      // 查找更上级的列表
      let parent = current.parentElement;
      while (parent && !['UL', 'OL'].includes(parent.tagName?.toLowerCase() || '')) {
        parent = parent.parentElement;
      }
      current = parent;
    } else {
      current = current.parentElement;
    }
  }

  return level;
}

// 获取列表项的缩进级别
function getListIndent(listItem) {
  const level = getListLevel(listItem);
  // 每级缩进2个空格
  return '  '.repeat(Math.max(0, level - 1));
}

// 获取当前选中的文本内容（改进版）
function getSelectedContent() {
  const selection = window.getSelection();
  if (selection.rangeCount === 0) {
    return null;
  }

  // 获取纯文本（向后兼容）
  const selectedText = selection.toString().trim();
  if (!selectedText) {
    return null;
  }

  // 获取格式化文本
  let formattedText = selectedText;

  try {
    // 尝试获取选中内容的HTML并转换为格式化文本
    const range = selection.getRangeAt(0);
    const container = document.createElement('div');
    container.appendChild(range.cloneContents());

    // 转换为保持格式的文本
    formattedText = htmlToFormattedText(container);

    // 清理多余的空行和空格
    formattedText = formattedText
      .replace(/\n\s*\n\s*\n/g, '\n\n') // 多个空行合并为两个
      .replace(/^\s+|\s+$/g, '') // 去除首尾空白
      .replace(/[ \t]+/g, ' '); // 多个空格合并为一个

  } catch (error) {
    console.warn('格式化文本提取失败，使用纯文本:', error);
    // 如果格式化失败，使用纯文本
    formattedText = selectedText;
  }

  return {
    text: selectedText, // 保持向后兼容
    formattedText: formattedText, // 新的格式化文本
    length: selectedText.length
  };
}

// 监听来自扩展的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.action === 'getSelectedContent') {
    const selectedContent = getSelectedContent();
    sendResponse({
      success: true,
      data: selectedContent
    });
  }
  return true;
});
