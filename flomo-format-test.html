<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flomo 格式测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .note {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <h1>Flomo 格式测试页面</h1>
    <div class="note">
        <strong>测试说明：</strong>选择下面的内容并使用右键菜单"保存到Flomo"，检查侧边栏中的格式化效果。
        现在只保留段落结构、粗体和下划线格式，其他格式都转换为普通文本。
    </div>

    <div class="test-section">
        <h2 class="test-title">1. 段落和换行测试</h2>
        <p>这是第一个段落，应该保持段落格式。</p>
        <p>这是第二个段落，<br>中间有一个换行符。</p>
        <p>这是第三个段落，用于测试段落之间的间距。</p>
        
        <div class="expected-result">期望结果：
这是第一个段落，应该保持段落格式。

这是第二个段落，
中间有一个换行符。

这是第三个段落，用于测试段落之间的间距。</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">2. 粗体文本测试</h2>
        <p>这段文本包含<strong>粗体内容</strong>和<b>另一种粗体</b>，应该保持HTML格式。</p>
        <p>普通文本和<strong>粗体文本</strong>混合显示。</p>
        
        <div class="expected-result">期望结果：
这段文本包含<strong>粗体内容</strong>和<strong>另一种粗体</strong>，应该保持HTML格式。

普通文本和<strong>粗体文本</strong>混合显示。</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">3. 下划线文本测试</h2>
        <p>这段文本包含<u>下划线内容</u>，应该保持HTML格式。</p>
        <p>普通文本和<u>下划线文本</u>以及<strong>粗体文本</strong>混合显示。</p>
        
        <div class="expected-result">期望结果：
这段文本包含<u>下划线内容</u>，应该保持HTML格式。

普通文本和<u>下划线文本</u>以及<strong>粗体文本</strong>混合显示。</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">4. 其他样式转换测试</h2>
        <p>这段文本包含各种样式：</p>
        <ul>
            <li><em>斜体文本</em>（应转换为普通文本）</li>
            <li><del>删除线文本</del>（应转换为普通文本）</li>
            <li><mark>高亮文本</mark>（应转换为普通文本）</li>
            <li>上标：E=mc<sup>2</sup>（应转换为普通文本）</li>
            <li>下标：H<sub>2</sub>O（应转换为普通文本）</li>
        </ul>
        
        <div class="expected-result">期望结果：
这段文本包含各种样式：

    斜体文本（应转换为普通文本）
    删除线文本（应转换为普通文本）
    高亮文本（应转换为普通文本）
    上标：E=mc2（应转换为普通文本）
    下标：H2O（应转换为普通文本）</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">5. 标题转换测试</h2>
        <h1>一级标题</h1>
        <h2>二级标题</h2>
        <h3>三级标题</h3>
        <p>标题应该转换为普通文本，保持内容但移除标题格式。</p>
        
        <div class="expected-result">期望结果：
标题转换测试

一级标题

二级标题

三级标题

标题应该转换为普通文本，保持内容但移除标题格式。</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">6. 列表转换测试</h2>
        <h3>无序列表：</h3>
        <ul>
            <li>第一项</li>
            <li>第二项
                <ul>
                    <li>嵌套项目</li>
                </ul>
            </li>
            <li>第三项</li>
        </ul>
        
        <div class="expected-result">期望结果：
无序列表：

第一项
第二项
  嵌套项目
第三项</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">7. 代码转换测试</h2>
        <p>这是行内代码：<code>console.log('Hello')</code></p>
        <p>这是代码块：</p>
        <pre><code>function test() {
    console.log('代码块测试');
    return true;
}</code></pre>
        
        <div class="expected-result">期望结果：
这是行内代码：console.log('Hello')

这是代码块：

function test() {
    console.log('代码块测试');
    return true;
}</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">8. 引用转换测试</h2>
        <blockquote>
            这是一个引用块，应该转换为普通文本。
        </blockquote>
        <p>引用后的普通段落。</p>
        
        <div class="expected-result">期望结果：
引用转换测试

这是一个引用块，应该转换为普通文本。

引用后的普通段落。</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">9. 链接转换测试</h2>
        <p>这里有一些链接：<a href="https://www.example.com">示例网站</a>和<a href="mailto:<EMAIL>">邮件地址</a>。</p>
        <p>链接应该只保留文本内容，移除URL。</p>
        
        <div class="expected-result">期望结果：
这里有一些链接：示例网站和邮件地址。

链接应该只保留文本内容，移除URL。</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">10. 复杂混合内容测试</h2>
        <h3>项目说明</h3>
        <p>这是一个<strong>重要项目</strong>，包含多种格式：</p>
        <ul>
            <li><strong>功能特性</strong>
                <ul>
                    <li>支持<em>多种格式</em></li>
                    <li>保持<u>基本结构</u></li>
                </ul>
            </li>
            <li>技术实现</li>
        </ul>
        <blockquote>
            使用现代技术实现，确保<strong>最佳性能</strong>。
        </blockquote>
        
        <div class="expected-result">期望结果：
项目说明

这是一个<strong>重要项目</strong>，包含多种格式：

  <strong>功能特性</strong>
    支持多种格式
    保持<u>基本结构</u>
  技术实现

使用现代技术实现，确保<strong>最佳性能</strong>。</div>
    </div>

    <script>
        // 添加选择提示
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.test-section');
            sections.forEach(section => {
                section.addEventListener('click', function() {
                    // 选中测试内容（排除期望结果）
                    const content = this.cloneNode(true);
                    const expectedResults = content.querySelectorAll('.expected-result');
                    expectedResults.forEach(el => el.remove());
                    
                    const range = document.createRange();
                    range.selectNodeContents(content);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                });
            });
            
            // 添加测试按钮
            const testButton = document.createElement('button');
            testButton.textContent = '🧪 在控制台测试格式化';
            testButton.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                background: #007acc;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                z-index: 1000;
                font-size: 14px;
            `;
            testButton.onclick = function() {
                console.log('🧪 请选择页面内容后运行以下命令测试：');
                console.log('testSelectedContent()');
                
                // 定义测试函数
                window.testSelectedContent = function() {
                    if (typeof getSelectedContent === 'function') {
                        const result = getSelectedContent();
                        if (result) {
                            console.log('✅ 选中内容：');
                            console.log('原始文本：', result.text);
                            console.log('格式化文本：', result.formattedText);
                            console.log('---');
                            return result;
                        } else {
                            console.log('❌ 没有选中内容');
                        }
                    } else {
                        console.error('❌ getSelectedContent 函数未找到');
                    }
                };
            };
            document.body.appendChild(testButton);
        });
    </script>
</body>
</html>
