<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格式保持功能测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .test-title {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }

        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }

        blockquote {
            border-left: 4px solid #007acc;
            margin: 0;
            padding-left: 20px;
            color: #666;
            font-style: italic;
        }

        .nested-list {
            margin: 10px 0;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        .highlight {
            background-color: yellow;
        }
    </style>
</head>

<body>
    <h1>格式保持功能测试页面</h1>
    <p>这个页面用于测试Chrome扩展的文本格式保持功能。请选择下面的各种内容来测试格式是否正确保持。</p>

    <div class="test-section">
        <h2 class="test-title">1. 标题层级测试</h2>
        <h1>一级标题</h1>
        <h2>二级标题</h2>
        <h3>三级标题</h3>
        <h4>四级标题</h4>
        <h5>五级标题</h5>
        <h6>六级标题</h6>
        <p>这些标题应该保持正确的层级关系。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">2. 段落和换行测试</h2>
        <p>这是第一个段落，包含一些普通文本。</p>
        <p>这是第二个段落，<br>中间有一个换行符。</p>
        <p>这是第三个段落，用于测试段落之间的间距。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">3. 列表结构测试</h2>
        <h3>无序列表：</h3>
        <ul>
            <li>第一项</li>
            <li>第二项
                <ul>
                    <li>嵌套项目 1</li>
                    <li>嵌套项目 2
                        <ul>
                            <li>深层嵌套项目</li>
                        </ul>
                    </li>
                </ul>
            </li>
            <li>第三项</li>
        </ul>

        <h3>有序列表：</h3>
        <ol>
            <li>第一步</li>
            <li>第二步
                <ol>
                    <li>子步骤 A</li>
                    <li>子步骤 B</li>
                </ol>
            </li>
            <li>第三步</li>
        </ol>

        <h3>混合列表：</h3>
        <ol>
            <li>有序项目 1</li>
            <li>有序项目 2
                <ul>
                    <li>无序子项目 A</li>
                    <li>无序子项目 B</li>
                </ul>
            </li>
            <li>有序项目 3</li>
        </ol>
    </div>

    <div class="test-section">
        <h2 class="test-title">4. 代码块和行内代码测试</h2>
        <p>这是一个行内代码示例：<code>console.log('Hello World')</code></p>

        <p>这是一个代码块：</p>
        <pre><code>function formatText(text) {
    // 保持缩进和格式
    if (text) {
        return text.trim();
    }
    return '';
}</code></pre>

        <p>另一个代码块（不同缩进）：</p>
        <pre><code>class TextFormatter {
    constructor() {
        this.options = {
            preserveWhitespace: true,
            keepIndentation: true
        };
    }
    
    format(input) {
        return this.processText(input);
    }
}</code></pre>
    </div>

    <div class="test-section">
        <h2 class="test-title">5. 引用块测试</h2>
        <blockquote>
            这是一个简单的引用块。
        </blockquote>

        <blockquote>
            这是一个多行的引用块。
            它包含多个段落。

            每个段落都应该保持引用格式。
        </blockquote>
    </div>

    <div class="test-section">
        <h2 class="test-title">6. 文本样式测试</h2>
        <p>这段文本包含各种样式：</p>
        <ul>
            <li><strong>粗体文本</strong> 和 <b>另一种粗体</b></li>
            <li><em>斜体文本</em> 和 <i>另一种斜体</i></li>
            <li><u>下划线文本</u></li>
            <li><del>删除线文本</del> 和 <s>另一种删除线</s></li>
            <li><mark class="highlight">高亮文本</mark></li>
            <li>上标：E=mc<sup>2</sup></li>
            <li>下标：H<sub>2</sub>O</li>
            <li>键盘按键：<kbd>Ctrl+C</kbd></li>
        </ul>

        <p>组合样式：<strong><em>粗体斜体</em></strong>，<code><strong>粗体代码</strong></code></p>
    </div>

    <div class="test-section">
        <h2 class="test-title">7. 链接测试</h2>
        <p>这里有一些链接：</p>
        <ul>
            <li><a href="https://www.example.com">普通链接</a></li>
            <li><a href="https://github.com">GitHub</a></li>
            <li><a href="mailto:<EMAIL>">邮件链接</a></li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">8. 表格测试</h2>
        <table>
            <thead>
                <tr>
                    <th>功能</th>
                    <th>状态</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>标题层级</td>
                    <td>✅ 完成</td>
                    <td>支持 H1-H6</td>
                </tr>
                <tr>
                    <td>列表结构</td>
                    <td>✅ 完成</td>
                    <td>支持嵌套列表</td>
                </tr>
                <tr>
                    <td>代码格式</td>
                    <td>✅ 完成</td>
                    <td>保持缩进</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-section">
        <h2 class="test-title">9. 复杂混合内容测试</h2>
        <h3>项目说明</h3>
        <p>这是一个<strong>复杂的文档</strong>，包含多种格式：</p>

        <ol>
            <li><strong>功能特性</strong>
                <ul>
                    <li>支持<em>多种格式</em></li>
                    <li>保持<code>原始结构</code></li>
                    <li><del>已废弃的功能</del></li>
                </ul>
            </li>
            <li><strong>技术实现</strong>
                <blockquote>
                    使用现代Web技术实现，确保<mark>最佳性能</mark>。
                </blockquote>
                <pre><code>// 示例代码
const config = {
    format: 'markdown',
    preserveStructure: true
};</code></pre>
            </li>
        </ol>

        <p>更多信息请访问：<a href="https://github.com/example/project">项目主页</a></p>
    </div>

    <script>
        // 添加选择提示
        document.addEventListener('DOMContentLoaded', function () {
            const sections = document.querySelectorAll('.test-section');
            sections.forEach(section => {
                section.addEventListener('click', function () {
                    // 选中整个测试区域
                    const range = document.createRange();
                    range.selectNodeContents(this);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                });
            });

            // 添加测试按钮
            const testButton = document.createElement('button');
            testButton.textContent = '🧪 测试格式化功能';
            testButton.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                background: #007acc;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                z-index: 1000;
                font-size: 14px;
            `;
            testButton.onclick = function () {
                // 加载并运行测试脚本
                const script = document.createElement('script');
                script.src = 'test-format.js';
                document.head.appendChild(script);
            };
            document.body.appendChild(testButton);
        });
    </script>
</body>

</html>